<?php declare(strict_types = 1);

// odsl-/Users/<USER>/Herd/workos-sac/app
return \PHPStan\Cache\CacheItem::__set_state(array(
   'variableKey' => 'v1',
   'data' => 
  array (
    '/Users/<USER>/Herd/workos-sac/app/Providers/AppServiceProvider.php' => 
    array (
      0 => '3484bb77c606e257390584e56ba5f3f0e7a6d473',
      1 => 
      array (
        0 => 'app\\providers\\appserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
        2 => 'app\\providers\\configurecarbon',
        3 => 'app\\providers\\configurecommands',
        4 => 'app\\providers\\configuremodels',
        5 => 'app\\providers\\configurepassworddefaults',
        6 => 'app\\providers\\configureurl',
        7 => 'app\\providers\\configurevite',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Providers/VoltServiceProvider.php' => 
    array (
      0 => '65a26a795005f380bc32bceec5c24b770dc58dbc',
      1 => 
      array (
        0 => 'app\\providers\\voltserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Providers/FolioServiceProvider.php' => 
    array (
      0 => '8cd87d7f787d11d68c1ff62e60629e3a9a97d365',
      1 => 
      array (
        0 => 'app\\providers\\folioserviceprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\register',
        1 => 'app\\providers\\boot',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Models/User.php' => 
    array (
      0 => 'e91cec5738f6381da1cdec542feb940fabb7c79e',
      1 => 
      array (
        0 => 'app\\models\\user',
      ),
      2 => 
      array (
        0 => 'app\\models\\getsecondarykeytype',
        1 => 'app\\models\\initials',
        2 => 'app\\models\\casts',
        3 => 'app\\models\\getslugoptions',
        4 => 'app\\models\\getroutekeyname',
        5 => 'app\\models\\getactivitylogoptions',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Http/Controllers/Controller.php' => 
    array (
      0 => '75cadca8afa5982965d1ac316df3c693271b4902',
      1 => 
      array (
        0 => 'app\\http\\controllers\\controller',
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Enums/SecondaryKeyType.php' => 
    array (
      0 => '999884fa02a91ed3b04101d5a58c4583b7de98fa',
      1 => 
      array (
        0 => 'app\\enums\\secondarykeytype',
      ),
      2 => 
      array (
        0 => 'app\\enums\\default',
        1 => 'app\\enums\\getlabel',
        2 => 'app\\enums\\getcolor',
        3 => 'app\\enums\\getdescription',
        4 => 'app\\enums\\geticon',
        5 => 'app\\enums\\usecases',
        6 => 'app\\enums\\storageinfo',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Providers/Filament/AdminPanelProvider.php' => 
    array (
      0 => 'fd1934dc4f020d0da2b1655a5428aeaaef50765a',
      1 => 
      array (
        0 => 'app\\providers\\filament\\adminpanelprovider',
      ),
      2 => 
      array (
        0 => 'app\\providers\\filament\\panel',
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/app/Traits/HasSecondaryUniqueKey.php' => 
    array (
      0 => '6734eb7a6735b1d33c13404d813e6d08bfba4c60',
      1 => 
      array (
        0 => 'app\\traits\\hassecondaryuniquekey',
      ),
      2 => 
      array (
        0 => 'app\\traits\\boothassecondaryuniquekey',
        1 => 'app\\traits\\findbysecondarykey',
        2 => 'app\\traits\\findbysecondarykeyorfail',
        3 => 'app\\traits\\generatesecondarykey',
        4 => 'app\\traits\\getsecondarykeytype',
        5 => 'app\\traits\\setsecondarykeytype',
        6 => 'app\\traits\\getsecondarykeycolumn',
        7 => 'app\\traits\\scopebysecondarykey',
        8 => 'app\\traits\\getroutekeyname',
        9 => 'app\\traits\\getkeytypeinfo',
      ),
      3 => 
      array (
      ),
    ),
  ),
));