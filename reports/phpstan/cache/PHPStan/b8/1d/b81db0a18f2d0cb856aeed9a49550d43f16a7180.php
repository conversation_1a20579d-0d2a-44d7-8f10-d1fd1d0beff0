<?php declare(strict_types = 1);

// odsl-/Users/<USER>/Herd/workos-sac/bootstrap
return \PHPStan\Cache\CacheItem::__set_state(array(
   'variableKey' => 'v1',
   'data' => 
  array (
    '/Users/<USER>/Herd/workos-sac/bootstrap/app.php' => 
    array (
      0 => 'd5714c3dea04fef87635ff071567884d81ad277f',
      1 => 
      array (
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
    '/Users/<USER>/Herd/workos-sac/bootstrap/providers.php' => 
    array (
      0 => '5cbdb68305d1afa69f122336fab1e96686173e61',
      1 => 
      array (
      ),
      2 => 
      array (
      ),
      3 => 
      array (
      ),
    ),
  ),
));