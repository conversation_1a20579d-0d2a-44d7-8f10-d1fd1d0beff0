# 2. Chinook Database Migrations Guide

## 2.1. Overview

This guide provides comprehensive instructions for creating Laravel database migrations to recreate the Chinook database schema. The migrations will establish the complete table structure, relationships, and constraints needed for the digital music store database.

## 2.2. Migration Creation Order

### 2.2.1. Dependency Order

Create migrations in this specific order to respect foreign key dependencies:

1. **Independent Tables**: `artists`, `genres`, `media_types`, `employees`
2. **Dependent Tables**: `albums`, `customers`, `playlists`
3. **Relationship Tables**: `tracks`, `invoices`
4. **Junction Tables**: `invoice_lines`, `playlist_track`

### 2.2.2. Generate Migration Commands

```bash
# Step 1: Independent tables
php artisan make:migration create_artists_table
php artisan make:migration create_genres_table
php artisan make:migration create_media_types_table
php artisan make:migration create_employees_table

# Step 2: Dependent tables
php artisan make:migration create_albums_table
php artisan make:migration create_customers_table
php artisan make:migration create_playlists_table

# Step 3: Relationship tables
php artisan make:migration create_tracks_table
php artisan make:migration create_invoices_table

# Step 4: Junction tables
php artisan make:migration create_invoice_lines_table
php artisan make:migration create_playlist_track_table
```

## 2.3. Migration Implementations

### 2.3.1. Artists Migration

```php
<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('artists', function (Blueprint $table) {
            $table->id();
            $table->string('name', 120)->nullable();
            
            // Indexes
            $table->index('name');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('artists');
    }
};
```

### 2.3.2. Genres Migration

```php
<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('genres', function (Blueprint $table) {
            $table->id();
            $table->string('name', 120)->nullable();
            
            // Indexes
            $table->index('name');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('genres');
    }
};
```

### 2.3.3. Media Types Migration

```php
<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('media_types', function (Blueprint $table) {
            $table->id();
            $table->string('name', 120)->nullable();
            
            // Indexes
            $table->index('name');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('media_types');
    }
};
```

### 2.3.4. Employees Migration

```php
<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('employees', function (Blueprint $table) {
            $table->id();
            $table->string('last_name', 20);
            $table->string('first_name', 20);
            $table->string('title', 30)->nullable();
            $table->foreignId('reports_to')->nullable()->constrained('employees')->onDelete('set null');
            $table->dateTime('birth_date')->nullable();
            $table->dateTime('hire_date')->nullable();
            $table->string('address', 70)->nullable();
            $table->string('city', 40)->nullable();
            $table->string('state', 40)->nullable();
            $table->string('country', 40)->nullable();
            $table->string('postal_code', 10)->nullable();
            $table->string('phone', 24)->nullable();
            $table->string('fax', 24)->nullable();
            $table->string('email', 60)->nullable();
            
            // Indexes
            $table->index('reports_to');
            $table->index(['last_name', 'first_name']);
            $table->index('email');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('employees');
    }
};
```

### 2.3.5. Albums Migration

```php
<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('albums', function (Blueprint $table) {
            $table->id();
            $table->string('title', 160);
            $table->foreignId('artist_id')->constrained('artists')->onDelete('cascade');
            
            // Indexes
            $table->index('artist_id');
            $table->index('title');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('albums');
    }
};
```

### 2.3.6. Customers Migration

```php
<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('first_name', 40);
            $table->string('last_name', 20);
            $table->string('company', 80)->nullable();
            $table->string('address', 70)->nullable();
            $table->string('city', 40)->nullable();
            $table->string('state', 40)->nullable();
            $table->string('country', 40)->nullable();
            $table->string('postal_code', 10)->nullable();
            $table->string('phone', 24)->nullable();
            $table->string('fax', 24)->nullable();
            $table->string('email', 60);
            $table->foreignId('support_rep_id')->nullable()->constrained('employees')->onDelete('set null');
            
            // Indexes
            $table->index('support_rep_id');
            $table->index(['last_name', 'first_name']);
            $table->index('email');
            $table->index('country');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
```

### 2.3.7. Playlists Migration

```php
<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('playlists', function (Blueprint $table) {
            $table->id();
            $table->string('name', 120)->nullable();
            
            // Indexes
            $table->index('name');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('playlists');
    }
};
```

### 2.3.8. Tracks Migration

```php
<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('tracks', function (Blueprint $table) {
            $table->id();
            $table->string('name', 200);
            $table->foreignId('album_id')->nullable()->constrained('albums')->onDelete('set null');
            $table->foreignId('media_type_id')->constrained('media_types')->onDelete('restrict');
            $table->foreignId('genre_id')->nullable()->constrained('genres')->onDelete('set null');
            $table->string('composer', 220)->nullable();
            $table->integer('milliseconds');
            $table->integer('bytes')->nullable();
            $table->decimal('unit_price', 10, 2);

            // Indexes
            $table->index('album_id');
            $table->index('media_type_id');
            $table->index('genre_id');
            $table->index('name');
            $table->index('unit_price');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('tracks');
    }
};
```

### 2.3.9. Invoices Migration

```php
<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('customer_id')->constrained('customers')->onDelete('cascade');
            $table->dateTime('invoice_date');
            $table->string('billing_address', 70)->nullable();
            $table->string('billing_city', 40)->nullable();
            $table->string('billing_state', 40)->nullable();
            $table->string('billing_country', 40)->nullable();
            $table->string('billing_postal_code', 10)->nullable();
            $table->decimal('total', 10, 2);

            // Indexes
            $table->index('customer_id');
            $table->index('invoice_date');
            $table->index('billing_country');
            $table->index('total');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
```

### 2.3.10. Invoice Lines Migration

```php
<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('invoice_lines', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained('invoices')->onDelete('cascade');
            $table->foreignId('track_id')->constrained('tracks')->onDelete('restrict');
            $table->decimal('unit_price', 10, 2);
            $table->integer('quantity');

            // Indexes
            $table->index('invoice_id');
            $table->index('track_id');
            $table->index(['invoice_id', 'track_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('invoice_lines');
    }
};
```

### 2.3.11. Playlist Track Migration

```php
<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('playlist_track', function (Blueprint $table) {
            $table->foreignId('playlist_id')->constrained('playlists')->onDelete('cascade');
            $table->foreignId('track_id')->constrained('tracks')->onDelete('cascade');

            // Composite primary key
            $table->primary(['playlist_id', 'track_id']);

            // Indexes
            $table->index('playlist_id');
            $table->index('track_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('playlist_track');
    }
};
```

## 2.4. Migration Best Practices

### 2.4.1. Foreign Key Constraints

- Use `constrained()` method for automatic foreign key naming
- Set appropriate `onDelete()` actions:
  - `cascade` for dependent records that should be deleted
  - `set null` for optional relationships
  - `restrict` to prevent deletion when references exist

### 2.4.2. Indexing Strategy

- Index all foreign key columns for performance
- Add composite indexes for common query patterns
- Index frequently searched columns (names, emails, etc.)
- Create composite primary keys for junction tables

### 2.4.3. Data Types

- Use appropriate string lengths matching the original schema
- Use `decimal(10, 2)` for monetary values with proper precision
- Use `dateTime` for timestamp fields
- Use `integer` for numeric IDs and counts

### 2.4.4. Migration Execution

```bash
# Run all migrations
php artisan migrate

# Check migration status
php artisan migrate:status

# Rollback last batch
php artisan migrate:rollback

# Reset all migrations
php artisan migrate:reset

# Fresh migration with seeding
php artisan migrate:fresh --seed
```

## 2.5. Next Steps

After creating these migrations, you should:

1. **Run Migrations**: `php artisan migrate`
2. **Create Factories**: See [Chinook Factories Guide](030-chinook-factories-guide.md)
3. **Create Seeders**: See [Chinook Seeders Guide](040-chinook-seeders-guide.md)

---

## Navigation

**← Previous:** [Chinook Models Guide](010-chinook-models-guide.md)

**Next →** [Chinook Factories Guide](030-chinook-factories-guide.md)
