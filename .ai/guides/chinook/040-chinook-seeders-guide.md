# 4. Chinook Database Seeders Guide

## 4.1. Overview

This guide provides comprehensive instructions for creating modern Laravel database seeders for the Chinook database schema. Seeders populate the database with sample data in the correct order to maintain referential integrity, using both the original Chinook data and factory-generated test data with full support for modern Laravel features.

**Modern Features Supported:**
- **Secondary Unique Keys**: Proper public_id generation and management
- **Slugs**: Automatic slug generation from public_id
- **User Stamps**: Realistic user assignment for audit trails
- **Tags**: Comprehensive tagging for categorization
- **Soft Deletes**: Proper handling of soft delete states
- **Enhanced Data**: Rich metadata and business-relevant seed data

## 4.2. Prerequisites

Before running seeders, ensure you have:

```bash
# Required users for user stamps
php artisan make:seeder UserSeeder

# Published tag migrations
php artisan vendor:publish --provider="Spatie\Tags\TagsServiceProvider" --tag="tags-migrations"
php artisan migrate
```

## 4.3. Seeder Creation Strategy

### 4.3.1. Seeding Order

Create seeders in dependency order to respect foreign key constraints:

1. **Foundation Data**: Users (for user stamps)
2. **Independent Data**: Artists, Genres, MediaTypes, Employees
3. **Dependent Data**: Albums, Customers, Playlists
4. **Relationship Data**: Tracks, Invoices
5. **Junction Data**: InvoiceLines, PlaylistTrack

### 4.3.2. Generate Seeder Commands

```bash
# Foundation seeder
php artisan make:seeder UserSeeder

# Create individual seeders
php artisan make:seeder ArtistSeeder
php artisan make:seeder GenreSeeder
php artisan make:seeder MediaTypeSeeder
php artisan make:seeder EmployeeSeeder
php artisan make:seeder AlbumSeeder
php artisan make:seeder CustomerSeeder
php artisan make:seeder PlaylistSeeder
php artisan make:seeder TrackSeeder
php artisan make:seeder InvoiceSeeder
php artisan make:seeder InvoiceLineSeeder
php artisan make:seeder PlaylistTrackSeeder

# Create main database seeder
php artisan make:seeder ChinookDatabaseSeeder
```

## 4.4. Seeder Implementations

### 4.4.1. User Seeder (Foundation)

```php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create system users for user stamps
        $systemUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );

        $adminUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]
        );

        // Create additional users for realistic user stamps
        User::factory()->count(10)->create();

        $this->command->info('Created system users and additional users for user stamps');
    }
}
```

### 4.4.2. Genre Seeder

```php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Genre;
use App\Models\User;
use Illuminate\Database\Seeder;

class GenreSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $systemUser = User::where('email', '<EMAIL>')->first();

        $genres = [
            [
                'id' => 1,
                'name' => 'Rock',
                'description' => 'A broad genre of popular music that originated as "rock and roll" in the United States in the late 1940s and early 1950s.',
                'color' => '#FF6B6B',
                'is_active' => true,
            ],
            [
                'id' => 2,
                'name' => 'Jazz',
                'description' => 'A music genre that originated in the African-American communities of New Orleans in the late 19th and early 20th centuries.',
                'color' => '#4ECDC4',
                'is_active' => true,
            ],
            [
                'id' => 3,
                'name' => 'Metal',
                'description' => 'A genre of rock music that developed in the late 1960s and early 1970s, largely in the United Kingdom and the United States.',
                'color' => '#45B7D1',
                'is_active' => true,
            ],
            [
                'id' => 4,
                'name' => 'Alternative & Punk',
                'description' => 'Alternative rock and punk music genres that emerged from the independent music underground.',
                'color' => '#96CEB4',
                'is_active' => true,
            ],
            [
                'id' => 5,
                'name' => 'Rock And Roll',
                'description' => 'A genre of popular music that evolved in the United States during the late 1940s and early 1950s.',
                'color' => '#FFEAA7',
                'is_active' => true,
            ],
            [
                'id' => 6,
                'name' => 'Blues',
                'description' => 'A music genre and musical form which originated in the Deep South of the United States around the 1860s.',
                'color' => '#DDA0DD',
                'is_active' => true,
            ],
            // Add more genres with enhanced data...
        ];

        foreach ($genres as $genreData) {
            $genre = Genre::updateOrCreate(
                ['id' => $genreData['id']],
                array_merge($genreData, [
                    'created_by' => $systemUser?->id,
                    'updated_by' => $systemUser?->id,
                ])
            );

            // Add tags based on genre type
            $tags = ['music', 'genre'];
            switch (strtolower($genre->name)) {
                case 'rock':
                    $tags = array_merge($tags, ['electric', 'guitar', 'drums']);
                    break;
                case 'jazz':
                    $tags = array_merge($tags, ['improvisation', 'swing', 'brass']);
                    break;
                case 'metal':
                    $tags = array_merge($tags, ['heavy', 'distortion', 'aggressive']);
                    break;
                default:
                    $tags = array_merge($tags, ['popular']);
            }

            $genre->syncTags($tags);
        }

        $this->command->info('Created genres with enhanced metadata and tags');
    }
}
```

### 4.3.2. MediaType Seeder

```php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\MediaType;
use Illuminate\Database\Seeder;

class MediaTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $mediaTypes = [
            ['id' => 1, 'name' => 'MPEG audio file'],
            ['id' => 2, 'name' => 'Protected AAC audio file'],
            ['id' => 3, 'name' => 'Protected MPEG-4 video file'],
            ['id' => 4, 'name' => 'Purchased AAC audio file'],
            ['id' => 5, 'name' => 'AAC audio file'],
        ];

        foreach ($mediaTypes as $mediaType) {
            MediaType::updateOrCreate(['id' => $mediaType['id']], $mediaType);
        }
    }
}
```

### 4.3.3. Employee Seeder

```php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Employee;
use Illuminate\Database\Seeder;

class EmployeeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $employees = [
            [
                'id' => 1,
                'last_name' => 'Adams',
                'first_name' => 'Andrew',
                'title' => 'General Manager',
                'reports_to' => null,
                'birth_date' => '1962-02-18 00:00:00',
                'hire_date' => '2002-08-14 00:00:00',
                'address' => '11120 Jasper Ave NW',
                'city' => 'Edmonton',
                'state' => 'AB',
                'country' => 'Canada',
                'postal_code' => 'T5K 2N1',
                'phone' => '+****************',
                'fax' => '+****************',
                'email' => '<EMAIL>',
            ],
            [
                'id' => 2,
                'last_name' => 'Edwards',
                'first_name' => 'Nancy',
                'title' => 'Sales Manager',
                'reports_to' => 1,
                'birth_date' => '1958-12-08 00:00:00',
                'hire_date' => '2002-05-01 00:00:00',
                'address' => '825 8 Ave SW',
                'city' => 'Calgary',
                'state' => 'AB',
                'country' => 'Canada',
                'postal_code' => 'T2P 2T3',
                'phone' => '+****************',
                'fax' => '+****************',
                'email' => '<EMAIL>',
            ],
            [
                'id' => 3,
                'last_name' => 'Peacock',
                'first_name' => 'Jane',
                'title' => 'Sales Support Agent',
                'reports_to' => 2,
                'birth_date' => '1973-08-29 00:00:00',
                'hire_date' => '2002-04-01 00:00:00',
                'address' => '1111 6 Ave SW',
                'city' => 'Calgary',
                'state' => 'AB',
                'country' => 'Canada',
                'postal_code' => 'T2P 5M5',
                'phone' => '+****************',
                'fax' => '+****************',
                'email' => '<EMAIL>',
            ],
            // Add more employees as needed...
        ];

        foreach ($employees as $employee) {
            Employee::updateOrCreate(['id' => $employee['id']], $employee);
        }

        // Create additional employees using factory if needed
        if (Employee::count() < 10) {
            Employee::factory()->count(10 - Employee::count())->create();
        }
    }
}
```

### 4.3.4. Artist Seeder

```php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Artist;
use Illuminate\Database\Seeder;

class ArtistSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed some famous artists from the original Chinook data
        $artists = [
            ['id' => 1, 'name' => 'AC/DC'],
            ['id' => 2, 'name' => 'Accept'],
            ['id' => 3, 'name' => 'Aerosmith'],
            ['id' => 4, 'name' => 'Alanis Morissette'],
            ['id' => 5, 'name' => 'Alice In Chains'],
            ['id' => 22, 'name' => 'Led Zeppelin'],
            ['id' => 50, 'name' => 'Metallica'],
            ['id' => 51, 'name' => 'Queen'],
            ['id' => 52, 'name' => 'Kiss'],
            ['id' => 58, 'name' => 'Deep Purple'],
            ['id' => 90, 'name' => 'Iron Maiden'],
            ['id' => 110, 'name' => 'Nirvana'],
            ['id' => 118, 'name' => 'Pearl Jam'],
            ['id' => 120, 'name' => 'Pink Floyd'],
            ['id' => 150, 'name' => 'U2'],
        ];

        foreach ($artists as $artist) {
            Artist::updateOrCreate(['id' => $artist['id']], $artist);
        }

        // Create additional artists using factory
        if (Artist::count() < 50) {
            Artist::factory()->count(50 - Artist::count())->create();
        }
    }
}
```

### 4.3.5. Album Seeder

```php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Album;
use App\Models\Artist;
use Illuminate\Database\Seeder;

class AlbumSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed some famous albums
        $albums = [
            ['id' => 1, 'title' => 'For Those About To Rock We Salute You', 'artist_id' => 1],
            ['id' => 4, 'title' => 'Let There Be Rock', 'artist_id' => 1],
            ['id' => 6, 'title' => 'Jagged Little Pill', 'artist_id' => 4],
            ['id' => 148, 'title' => 'Black Album', 'artist_id' => 50],
            ['id' => 183, 'title' => 'Dark Side Of The Moon', 'artist_id' => 120],
            ['id' => 185, 'title' => 'Greatest Hits I', 'artist_id' => 51],
        ];

        foreach ($albums as $album) {
            if (Artist::find($album['artist_id'])) {
                Album::updateOrCreate(['id' => $album['id']], $album);
            }
        }

        // Create additional albums using factory
        $artists = Artist::all();
        if ($artists->isNotEmpty() && Album::count() < 100) {
            Album::factory()
                ->count(100 - Album::count())
                ->recycle($artists)
                ->create();
        }
    }
}
```

### 4.3.6. Customer Seeder

```php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Customer;
use App\Models\Employee;
use Illuminate\Database\Seeder;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get available support representatives
        $supportReps = Employee::whereIn('title', [
            'Sales Support Agent',
            'Sales Manager'
        ])->get();

        if ($supportReps->isEmpty()) {
            $supportReps = Employee::all();
        }

        // Create customers using factory
        if ($supportReps->isNotEmpty()) {
            Customer::factory()
                ->count(100)
                ->recycle($supportReps)
                ->create();
        }
    }
}
```

### 4.3.7. Track Seeder

```php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Album;
use App\Models\Genre;
use App\Models\MediaType;
use App\Models\Track;
use Illuminate\Database\Seeder;

class TrackSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $albums = Album::all();
        $genres = Genre::all();
        $mediaTypes = MediaType::all();

        if ($albums->isNotEmpty() && $genres->isNotEmpty() && $mediaTypes->isNotEmpty()) {
            // Create tracks for each album
            foreach ($albums as $album) {
                Track::factory()
                    ->count(rand(8, 15)) // Random number of tracks per album
                    ->forAlbum($album)
                    ->recycle($genres)
                    ->recycle($mediaTypes)
                    ->create();
            }
        }
    }
}
```

### 4.3.8. Playlist Seeder

```php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Playlist;
use Illuminate\Database\Seeder;

class PlaylistSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $playlists = [
            ['id' => 1, 'name' => 'Music'],
            ['id' => 2, 'name' => 'Movies'],
            ['id' => 3, 'name' => 'TV Shows'],
            ['id' => 4, 'name' => 'Audiobooks'],
            ['id' => 5, 'name' => "90's Music"],
            ['id' => 8, 'name' => 'Music'],
            ['id' => 9, 'name' => 'Music Videos'],
            ['id' => 10, 'name' => 'TV Shows'],
            ['id' => 11, 'name' => 'Brazilian Music'],
            ['id' => 12, 'name' => 'Classical'],
            ['id' => 13, 'name' => 'Classical 101 - Deep Cuts'],
            ['id' => 14, 'name' => 'Classical 101 - Next Steps'],
            ['id' => 15, 'name' => 'Classical 101 - The Basics'],
            ['id' => 16, 'name' => 'Grunge'],
            ['id' => 17, 'name' => 'Heavy Metal Classic'],
            ['id' => 18, 'name' => 'On-The-Go 1'],
        ];

        foreach ($playlists as $playlist) {
            Playlist::updateOrCreate(['id' => $playlist['id']], $playlist);
        }

        // Create additional playlists using factory
        if (Playlist::count() < 25) {
            Playlist::factory()->count(25 - Playlist::count())->create();
        }
    }
}
```

### 4.3.9. Invoice Seeder

```php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Customer;
use App\Models\Invoice;
use Illuminate\Database\Seeder;

class InvoiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $customers = Customer::all();

        if ($customers->isNotEmpty()) {
            // Create invoices for customers
            foreach ($customers as $customer) {
                Invoice::factory()
                    ->count(rand(1, 8)) // Random number of invoices per customer
                    ->forCustomer($customer)
                    ->create();
            }
        }
    }
}
```

### 4.3.10. Invoice Line Seeder

```php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Invoice;
use App\Models\InvoiceLine;
use App\Models\Track;
use Illuminate\Database\Seeder;

class InvoiceLineSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $invoices = Invoice::all();
        $tracks = Track::all();

        if ($invoices->isNotEmpty() && $tracks->isNotEmpty()) {
            foreach ($invoices as $invoice) {
                $lineCount = rand(1, 10);
                $selectedTracks = $tracks->random($lineCount);
                $total = 0;

                foreach ($selectedTracks as $track) {
                    $quantity = rand(1, 3);
                    $unitPrice = $track->unit_price;

                    InvoiceLine::create([
                        'invoice_id' => $invoice->id,
                        'track_id' => $track->id,
                        'unit_price' => $unitPrice,
                        'quantity' => $quantity,
                    ]);

                    $total += $unitPrice * $quantity;
                }

                // Update invoice total
                $invoice->update(['total' => $total]);
            }
        }
    }
}
```

### 4.3.11. Playlist Track Seeder

```php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Playlist;
use App\Models\Track;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PlaylistTrackSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $playlists = Playlist::all();
        $tracks = Track::all();

        if ($playlists->isNotEmpty() && $tracks->isNotEmpty()) {
            foreach ($playlists as $playlist) {
                $trackCount = rand(10, 50);
                $selectedTracks = $tracks->random($trackCount);

                foreach ($selectedTracks as $track) {
                    DB::table('playlist_track')->updateOrInsert([
                        'playlist_id' => $playlist->id,
                        'track_id' => $track->id,
                    ]);
                }
            }
        }
    }
}
```

## 4.4. Main Database Seeder

### 4.4.1. ChinookDatabaseSeeder

```php
<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ChinookDatabaseSeeder extends Seeder
{
    /**
     * Seed the Chinook database.
     */
    public function run(): void
    {
        $this->call([
            // Step 1: Independent tables
            GenreSeeder::class,
            MediaTypeSeeder::class,
            EmployeeSeeder::class,
            ArtistSeeder::class,

            // Step 2: Dependent tables
            AlbumSeeder::class,
            CustomerSeeder::class,
            PlaylistSeeder::class,

            // Step 3: Relationship tables
            TrackSeeder::class,
            InvoiceSeeder::class,

            // Step 4: Junction tables
            InvoiceLineSeeder::class,
            PlaylistTrackSeeder::class,
        ]);
    }
}
```

## 4.5. Seeding Commands

### 4.5.1. Running Seeders

```bash
# Run all seeders
php artisan db:seed

# Run specific seeder
php artisan db:seed --class=ChinookDatabaseSeeder

# Fresh migration with seeding
php artisan migrate:fresh --seed

# Run seeder in production (with confirmation)
php artisan db:seed --force
```

### 4.5.2. Updating Main DatabaseSeeder

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            ChinookDatabaseSeeder::class,
        ]);
    }
}
```

## 4.6. Best Practices

### 4.6.1. Data Integrity

- Always use `updateOrCreate()` for fixed data to prevent duplicates
- Respect foreign key constraints by seeding in dependency order
- Use transactions for complex seeding operations
- Validate data before insertion

### 4.6.2. Performance Optimization

- Use `DB::table()->insert()` for large datasets
- Disable foreign key checks temporarily for bulk operations
- Use chunking for very large datasets
- Consider using database-specific bulk insert methods

### 4.6.3. Environment Considerations

- Use different seeding strategies for development vs. production
- Implement conditional seeding based on environment
- Use factories for test data, real data for production seeds
- Document any production-specific seeding requirements

## 4.7. Modern Seeding Features

### 4.7.1. User Stamps Integration

All seeders properly set user stamps:

```php
$systemUser = User::where('email', '<EMAIL>')->first();

$model = Model::updateOrCreate(
    ['id' => $data['id']],
    array_merge($data, [
        'created_by' => $systemUser?->id,
        'updated_by' => $systemUser?->id,
    ])
);
```

### 4.7.2. Tags Integration

Seeders automatically add relevant tags:

```php
// Genre seeder adds contextual tags
$genre->syncTags(['music', 'genre', 'rock', 'electric']);

// Artist seeder adds style tags
$artist->syncTags(['rock', 'classic-rock', 'british']);
```

### 4.7.3. Secondary Keys and Slugs

Models automatically generate public_id and slug values:

```php
$artist = Artist::create(['name' => 'The Beatles']);
// public_id and slug are generated automatically by traits
```

### 4.7.4. Enhanced Data Seeding

All seeders include rich metadata:

```php
$genre = Genre::create([
    'name' => 'Rock',
    'description' => 'A broad genre of popular music...',
    'color' => '#FF6B6B',
    'is_active' => true,
]);
```

## 4.8. Seeding Commands

### 4.8.1. Running Seeders

```bash
# Run all seeders
php artisan db:seed

# Run specific seeder
php artisan db:seed --class=ChinookDatabaseSeeder

# Fresh migration with seeding
php artisan migrate:fresh --seed

# Run seeder in production (with confirmation)
php artisan db:seed --force

# Run with specific environment
php artisan db:seed --env=production
```

### 4.8.2. Updating Main DatabaseSeeder

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            UserSeeder::class, // Must be first for user stamps
            ChinookDatabaseSeeder::class,
        ]);
    }
}
```

## 4.9. Best Practices

### 4.9.1. Data Integrity

- Always seed Users first for user stamps functionality
- Use `updateOrCreate()` for fixed data to prevent duplicates
- Respect foreign key constraints by seeding in dependency order
- Use transactions for complex seeding operations
- Validate data before insertion

### 4.9.2. Performance Optimization

- Use `DB::table()->insert()` for large datasets
- Disable foreign key checks temporarily for bulk operations
- Use chunking for very large datasets
- Consider using database-specific bulk insert methods

### 4.9.3. Environment Considerations

- Use different seeding strategies for development vs. production
- Implement conditional seeding based on environment
- Use factories for test data, real data for production seeds
- Document any production-specific seeding requirements

### 4.9.4. Modern Laravel Features

- Ensure user stamps are properly configured
- Use tags for flexible categorization
- Leverage secondary unique keys for API endpoints
- Implement proper soft delete handling

## 4.10. Next Steps

After creating these seeders:

1. **Update DatabaseSeeder**: Add `UserSeeder::class` and `ChinookDatabaseSeeder::class` to your main `DatabaseSeeder`
2. **Run Seeders**: Execute `php artisan db:seed --class=ChinookDatabaseSeeder`
3. **Test Data**: Verify the seeded data maintains referential integrity
4. **Create Tests**: Write tests to validate your seeded data structure
5. **Configure Tags**: Ensure Spatie Tags is working correctly
6. **Verify User Stamps**: Check that created_by/updated_by are properly set

---

## Navigation

**← Previous:** [Chinook Factories Guide](030-chinook-factories-guide.md)
