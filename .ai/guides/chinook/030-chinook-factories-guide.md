# 3. Chinook Database Factories Guide

## 3.1. Overview

This guide provides comprehensive instructions for creating Laravel model factories for the Chinook database schema. Factories generate realistic test data that respects database relationships and constraints, making them essential for testing and development.

## 3.2. Factory Creation Commands

### 3.2.1. Generate All Factories

```bash
# Core music factories
php artisan make:factory ArtistFactory --model=Artist
php artisan make:factory AlbumFactory --model=Album
php artisan make:factory TrackFactory --model=Track
php artisan make:factory GenreFactory --model=Genre
php artisan make:factory MediaTypeFactory --model=MediaType

# Customer and employee factories
php artisan make:factory CustomerFactory --model=Customer
php artisan make:factory EmployeeFactory --model=Employee

# Sales factories
php artisan make:factory InvoiceFactory --model=Invoice
php artisan make:factory InvoiceLineFactory --model=InvoiceLine

# Playlist factory
php artisan make:factory PlaylistFactory --model=Playlist
```

## 3.3. Factory Implementations

### 3.3.1. Artist Factory

```php
<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Artist;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Artist>
 */
class ArtistFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Artist::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->randomElement([
                'The Beatles',
                'Led Zeppelin',
                'Pink Floyd',
                'Queen',
                'The Rolling Stones',
                'AC/DC',
                'Metallica',
                'Nirvana',
                'Radiohead',
                'U2',
            ]) ?? $this->faker->company(),
        ];
    }

    /**
     * Create a rock artist.
     */
    public function rock(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $this->faker->randomElement([
                'Iron Maiden',
                'Black Sabbath',
                'Deep Purple',
                'Judas Priest',
                'Motorhead',
            ]),
        ]);
    }

    /**
     * Create a jazz artist.
     */
    public function jazz(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $this->faker->randomElement([
                'Miles Davis',
                'John Coltrane',
                'Duke Ellington',
                'Charlie Parker',
                'Thelonious Monk',
            ]),
        ]);
    }
}
```

### 3.3.2. Genre Factory

```php
<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Genre;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Genre>
 */
class GenreFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Genre::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->randomElement([
                'Rock',
                'Jazz',
                'Metal',
                'Alternative & Punk',
                'Rock And Roll',
                'Blues',
                'Latin',
                'Reggae',
                'Pop',
                'Soundtrack',
                'Bossa Nova',
                'Easy Listening',
                'Heavy Metal',
                'R&B/Soul',
                'Electronica/Dance',
                'World',
                'Hip Hop/Rap',
                'Classical',
                'Opera',
                'Country',
                'Folk',
                'Indie',
            ]),
        ];
    }
}
```

### 3.3.3. MediaType Factory

```php
<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\MediaType;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MediaType>
 */
class MediaTypeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = MediaType::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->randomElement([
                'MPEG audio file',
                'Protected AAC audio file',
                'Protected MPEG-4 video file',
                'Purchased AAC audio file',
                'AAC audio file',
                'FLAC audio file',
                'WAV audio file',
                'OGG audio file',
            ]),
        ];
    }
}
```

### 3.3.4. Album Factory

```php
<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Album;
use App\Models\Artist;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Album>
 */
class AlbumFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Album::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(rand(1, 4), false),
            'artist_id' => Artist::factory(),
        ];
    }

    /**
     * Create an album for an existing artist.
     */
    public function forArtist(Artist $artist): static
    {
        return $this->state(fn (array $attributes) => [
            'artist_id' => $artist->id,
        ]);
    }

    /**
     * Create a classic rock album.
     */
    public function classicRock(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => $this->faker->randomElement([
                'Dark Side of the Moon',
                'Led Zeppelin IV',
                'Abbey Road',
                'The Wall',
                'Back in Black',
                'Rumours',
                'Hotel California',
                'Born to Run',
            ]),
        ]);
    }
}
```

### 3.3.5. Employee Factory

```php
<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Employee;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Employee>
 */
class EmployeeFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Employee::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'last_name' => $this->faker->lastName(),
            'first_name' => $this->faker->firstName(),
            'title' => $this->faker->randomElement([
                'General Manager',
                'Sales Manager',
                'Sales Support Agent',
                'IT Manager',
                'IT Staff',
                'Customer Service Representative',
                'Marketing Manager',
                'Accountant',
            ]),
            'reports_to' => null, // Will be set by relationships
            'birth_date' => $this->faker->dateTimeBetween('-65 years', '-25 years'),
            'hire_date' => $this->faker->dateTimeBetween('-20 years', '-1 year'),
            'address' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->stateAbbr(),
            'country' => $this->faker->country(),
            'postal_code' => $this->faker->postcode(),
            'phone' => $this->faker->phoneNumber(),
            'fax' => $this->faker->phoneNumber(),
            'email' => $this->faker->unique()->safeEmail(),
        ];
    }

    /**
     * Create a manager employee.
     */
    public function manager(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => $this->faker->randomElement([
                'General Manager',
                'Sales Manager',
                'IT Manager',
                'Marketing Manager',
            ]),
        ]);
    }

    /**
     * Create an employee that reports to a manager.
     */
    public function reportsTo(Employee $manager): static
    {
        return $this->state(fn (array $attributes) => [
            'reports_to' => $manager->id,
            'title' => $this->faker->randomElement([
                'Sales Support Agent',
                'IT Staff',
                'Customer Service Representative',
                'Accountant',
            ]),
        ]);
    }
}
```

### 3.3.6. Customer Factory

```php
<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Customer;
use App\Models\Employee;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customer>
 */
class CustomerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Customer::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'first_name' => $this->faker->firstName(),
            'last_name' => $this->faker->lastName(),
            'company' => $this->faker->optional(0.3)->company(),
            'address' => $this->faker->streetAddress(),
            'city' => $this->faker->city(),
            'state' => $this->faker->optional(0.7)->stateAbbr(),
            'country' => $this->faker->country(),
            'postal_code' => $this->faker->postcode(),
            'phone' => $this->faker->phoneNumber(),
            'fax' => $this->faker->optional(0.2)->phoneNumber(),
            'email' => $this->faker->unique()->safeEmail(),
            'support_rep_id' => Employee::factory(),
        ];
    }

    /**
     * Create a customer with a specific support representative.
     */
    public function withSupportRep(Employee $employee): static
    {
        return $this->state(fn (array $attributes) => [
            'support_rep_id' => $employee->id,
        ]);
    }
}
```

### 3.3.7. Track Factory

```php
<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Album;
use App\Models\Genre;
use App\Models\MediaType;
use App\Models\Track;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Track>
 */
class TrackFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Track::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->sentence(rand(1, 5), false),
            'album_id' => Album::factory(),
            'media_type_id' => MediaType::factory(),
            'genre_id' => Genre::factory(),
            'composer' => $this->faker->optional(0.7)->name(),
            'milliseconds' => $this->faker->numberBetween(30000, 600000), // 30 seconds to 10 minutes
            'bytes' => $this->faker->numberBetween(1000000, 50000000), // 1MB to 50MB
            'unit_price' => $this->faker->randomElement(['0.99', '1.29', '1.99']),
        ];
    }

    /**
     * Create a track for a specific album.
     */
    public function forAlbum(Album $album): static
    {
        return $this->state(fn (array $attributes) => [
            'album_id' => $album->id,
        ]);
    }

    /**
     * Create a short track (under 3 minutes).
     */
    public function short(): static
    {
        return $this->state(fn (array $attributes) => [
            'milliseconds' => $this->faker->numberBetween(30000, 180000),
        ]);
    }

    /**
     * Create a long track (over 6 minutes).
     */
    public function long(): static
    {
        return $this->state(fn (array $attributes) => [
            'milliseconds' => $this->faker->numberBetween(360000, 1200000),
        ]);
    }
}
```

### 3.3.8. Invoice Factory

```php
<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Customer;
use App\Models\Invoice;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Invoice>
 */
class InvoiceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Invoice::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'customer_id' => Customer::factory(),
            'invoice_date' => $this->faker->dateTimeBetween('-2 years', 'now'),
            'billing_address' => $this->faker->streetAddress(),
            'billing_city' => $this->faker->city(),
            'billing_state' => $this->faker->optional(0.7)->stateAbbr(),
            'billing_country' => $this->faker->country(),
            'billing_postal_code' => $this->faker->postcode(),
            'total' => $this->faker->randomFloat(2, 0.99, 99.99),
        ];
    }

    /**
     * Create an invoice for a specific customer.
     */
    public function forCustomer(Customer $customer): static
    {
        return $this->state(fn (array $attributes) => [
            'customer_id' => $customer->id,
            'billing_address' => $customer->address,
            'billing_city' => $customer->city,
            'billing_state' => $customer->state,
            'billing_country' => $customer->country,
            'billing_postal_code' => $customer->postal_code,
        ]);
    }
}
```

## 3.4. Factory Relationships

### 3.4.1. Using Factories with Relationships

```php
// Create an artist with albums
$artist = Artist::factory()
    ->has(Album::factory()->count(3))
    ->create();

// Create an album with tracks
$album = Album::factory()
    ->for(Artist::factory())
    ->has(Track::factory()->count(10))
    ->create();

// Create a customer with invoices
$customer = Customer::factory()
    ->for(Employee::factory()->manager(), 'supportRep')
    ->has(Invoice::factory()->count(5))
    ->create();
```

### 3.4.2. Complex Factory Scenarios

```php
// Create a complete music catalog
$genres = Genre::factory()->count(10)->create();
$mediaTypes = MediaType::factory()->count(5)->create();

$artists = Artist::factory()->count(20)->create();

$albums = Album::factory()
    ->count(50)
    ->recycle($artists)
    ->create();

$tracks = Track::factory()
    ->count(500)
    ->recycle($albums)
    ->recycle($genres)
    ->recycle($mediaTypes)
    ->create();
```

## 3.5. Next Steps

After creating these factories, you should:

1. **Create Seeders**: See [Chinook Seeders Guide](040-chinook-seeders-guide.md)
2. **Test Factories**: Run `php artisan tinker` and test factory creation
3. **Use in Tests**: Implement factories in your test suite

---

## Navigation

**← Previous:** [Chinook Migrations Guide](020-chinook-migrations-guide.md)

**Next →** [Chinook Seeders Guide](040-chinook-seeders-guide.md)
