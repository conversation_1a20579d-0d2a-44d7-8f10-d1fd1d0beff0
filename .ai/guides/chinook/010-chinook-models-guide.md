# 1. Chinook Database Models Guide

## 1.1. Overview

This guide provides comprehensive instructions for creating Laravel Eloquent models for the Chinook database schema. The Chinook database represents a digital music store with artists, albums, tracks, customers, employees, and sales data.

## 1.2. Database Schema Overview

The Chinook database consists of 11 interconnected tables:

- **Core Music Data**: `artists`, `albums`, `tracks`, `genres`, `media_types`
- **Customer Management**: `customers`, `employees`
- **Sales System**: `invoices`, `invoice_lines`
- **Playlist System**: `playlists`, `playlist_track`

## 1.3. Model Creation Commands

### 1.3.1. Generate All Models

```bash
# Core music models
php artisan make:model Artist
php artisan make:model Album
php artisan make:model Track
php artisan make:model Genre
php artisan make:model MediaType

# Customer and employee models
php artisan make:model Customer
php artisan make:model Employee

# Sales models
php artisan make:model Invoice
php artisan make:model InvoiceLine

# Playlist models
php artisan make:model Playlist
```

## 1.4. Model Implementations

### 1.4.1. Artist Model

```php
<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Artist extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'artists';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
    ];

    /**
     * Get the albums for the artist.
     */
    public function albums(): HasMany
    {
        return $this->hasMany(Album::class);
    }

    /**
     * Get all tracks for this artist through albums.
     */
    public function tracks(): HasMany
    {
        return $this->hasManyThrough(Track::class, Album::class);
    }
}
```

### 1.4.2. Album Model

```php
<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Album extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'albums';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'title',
        'artist_id',
    ];

    /**
     * Get the artist that owns the album.
     */
    public function artist(): BelongsTo
    {
        return $this->belongsTo(Artist::class);
    }

    /**
     * Get the tracks for the album.
     */
    public function tracks(): HasMany
    {
        return $this->hasMany(Track::class);
    }
}
```

### 1.4.3. Track Model

```php
<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Track extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'tracks';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'album_id',
        'media_type_id',
        'genre_id',
        'composer',
        'milliseconds',
        'bytes',
        'unit_price',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'unit_price' => 'decimal:2',
        'milliseconds' => 'integer',
        'bytes' => 'integer',
    ];

    /**
     * Get the album that owns the track.
     */
    public function album(): BelongsTo
    {
        return $this->belongsTo(Album::class);
    }

    /**
     * Get the genre that owns the track.
     */
    public function genre(): BelongsTo
    {
        return $this->belongsTo(Genre::class);
    }

    /**
     * Get the media type that owns the track.
     */
    public function mediaType(): BelongsTo
    {
        return $this->belongsTo(MediaType::class);
    }

    /**
     * Get the artist through the album relationship.
     */
    public function artist(): BelongsTo
    {
        return $this->album()->artist();
    }

    /**
     * Get the invoice lines for the track.
     */
    public function invoiceLines(): HasMany
    {
        return $this->hasMany(InvoiceLine::class);
    }

    /**
     * Get the playlists that contain this track.
     */
    public function playlists(): BelongsToMany
    {
        return $this->belongsToMany(Playlist::class, 'playlist_track');
    }
}
```

### 1.4.4. Genre Model

```php
<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Genre extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'genres';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
    ];

    /**
     * Get the tracks for the genre.
     */
    public function tracks(): HasMany
    {
        return $this->hasMany(Track::class);
    }
}
```

### 1.4.5. MediaType Model

```php
<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MediaType extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'media_types';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
    ];

    /**
     * Get the tracks for the media type.
     */
    public function tracks(): HasMany
    {
        return $this->hasMany(Track::class);
    }
}
```

## 1.5. Key Model Features

### 1.5.1. Timestamps Configuration

All Chinook models have `public $timestamps = false` because the original schema doesn't include `created_at` and `updated_at` columns.

### 1.5.2. Fillable Attributes

Each model defines `$fillable` arrays to specify which attributes can be mass-assigned, following Laravel security best practices.

### 1.5.3. Type Casting

Models use `$casts` arrays to ensure proper data types, especially for decimal prices and integer values.

### 1.5.4. Relationship Methods

All models include comprehensive relationship methods using Laravel's Eloquent relationship types:
- `belongsTo()` for foreign key relationships
- `hasMany()` for one-to-many relationships
- `hasManyThrough()` for indirect relationships
- `belongsToMany()` for many-to-many relationships

### 1.4.6. Customer Model

```php
<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'customers';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'company',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'phone',
        'fax',
        'email',
        'support_rep_id',
    ];

    /**
     * Get the support representative for the customer.
     */
    public function supportRep(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'support_rep_id');
    }

    /**
     * Get the invoices for the customer.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get the customer's full name.
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }
}
```

### 1.4.7. Employee Model

```php
<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Employee extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'employees';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'last_name',
        'first_name',
        'title',
        'reports_to',
        'birth_date',
        'hire_date',
        'address',
        'city',
        'state',
        'country',
        'postal_code',
        'phone',
        'fax',
        'email',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'birth_date' => 'datetime',
        'hire_date' => 'datetime',
    ];

    /**
     * Get the manager that this employee reports to.
     */
    public function manager(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'reports_to');
    }

    /**
     * Get the employees that report to this employee.
     */
    public function subordinates(): HasMany
    {
        return $this->hasMany(Employee::class, 'reports_to');
    }

    /**
     * Get the customers assigned to this employee.
     */
    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class, 'support_rep_id');
    }

    /**
     * Get the employee's full name.
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }
}
```

### 1.4.8. Invoice Model

```php
<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Invoice extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'invoices';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'customer_id',
        'invoice_date',
        'billing_address',
        'billing_city',
        'billing_state',
        'billing_country',
        'billing_postal_code',
        'total',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'invoice_date' => 'datetime',
        'total' => 'decimal:2',
    ];

    /**
     * Get the customer that owns the invoice.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the invoice lines for the invoice.
     */
    public function invoiceLines(): HasMany
    {
        return $this->hasMany(InvoiceLine::class);
    }
}
```

### 1.4.9. InvoiceLine Model

```php
<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InvoiceLine extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'invoice_lines';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'invoice_id',
        'track_id',
        'unit_price',
        'quantity',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'unit_price' => 'decimal:2',
        'quantity' => 'integer',
    ];

    /**
     * Get the invoice that owns the invoice line.
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Get the track that owns the invoice line.
     */
    public function track(): BelongsTo
    {
        return $this->belongsTo(Track::class);
    }

    /**
     * Calculate the line total (unit_price * quantity).
     */
    public function getLineTotalAttribute(): string
    {
        return number_format($this->unit_price * $this->quantity, 2);
    }
}
```

### 1.4.10. Playlist Model

```php
<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Playlist extends Model
{
    /**
     * The table associated with the model.
     */
    protected $table = 'playlists';

    /**
     * Indicates if the model should be timestamped.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
    ];

    /**
     * Get the tracks that belong to this playlist.
     */
    public function tracks(): BelongsToMany
    {
        return $this->belongsToMany(Track::class, 'playlist_track');
    }
}
```

## 1.6. Model Usage Examples

### 1.6.1. Basic Queries

```php
// Get all artists with their albums
$artists = Artist::with('albums')->get();

// Get tracks with their album and artist information
$tracks = Track::with(['album.artist', 'genre', 'mediaType'])->get();

// Get customer with their invoices and invoice lines
$customer = Customer::with(['invoices.invoiceLines.track'])->find(1);
```

### 1.6.2. Complex Relationships

```php
// Get all tracks by a specific artist
$artistTracks = Artist::find(1)->tracks;

// Get employee hierarchy
$manager = Employee::with('subordinates.subordinates')->find(1);

// Get playlist with track details
$playlist = Playlist::with(['tracks.album.artist', 'tracks.genre'])->find(1);
```

### 1.6.3. Calculated Attributes

```php
// Customer full name
$customerName = $customer->full_name;

// Invoice line total
$lineTotal = $invoiceLine->line_total;

// Employee full name
$employeeName = $employee->full_name;
```

## 1.7. Next Steps

After creating these models, you should:

1. **Create Migrations**: See [Chinook Migrations Guide](020-chinook-migrations-guide.md)
2. **Create Factories**: See [Chinook Factories Guide](030-chinook-factories-guide.md)
3. **Create Seeders**: See [Chinook Seeders Guide](040-chinook-seeders-guide.md)

---

## Navigation

**Next →** [Chinook Migrations Guide](020-chinook-migrations-guide.md)
